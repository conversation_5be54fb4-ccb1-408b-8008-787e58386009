<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\admin\controller\Common;
use think\facade\Db;

use app\validate\Users as UsersValidate;
use think\exception\ValidateException;

class User extends Common
{
    //用户列表
    public function index()
    {
        $params = request()->param();
        $where = [];

        if (!empty($params['keyword'])) {
            $where[] = ["email|first_name|last_name", "like", "%" . $params['keyword'] . "%"];
        }

        $List = Db::name('User')
            ->where($where)
            ->order("id desc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ])
            ->each(function ($item) {
                $item['role_name'] = Db::name("Roles")->where("id", $item['role_id'])->value("name");
                return $item;
            });

        return view("", [
            "List" => $List,
            "params" => $params,
        ]);
    }

    //添加用户
    public function add()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            //验证数据
            try {
                validate(UsersValidate::class)->scene('back-add')->check($data);
            } catch (ValidateException $e) {
                // 验证失败 输出错误信息
                return $this->error($e->getError());
            }

            // 生成salt
            $data['salt'] = generateSalt();
            // 明码进行加盐hash解决
            $data['password'] = generateHashPassword(trim($data['password']), $data['salt']);

            $data['create_time'] = date("Y-m-d H:i:s");
            $s = Db::name("User")->insertGetId($data);
            if ($s) {
                $this->success('添加成功！');
            } else {
                $this->error("添加失败，请重试！");
            }
        } else {
            $country = getCountry();

            $roles = Db::name("Roles")->select();

            return view("", [
                'country' => $country,
                "roles" => $roles,
            ]);
        }
    }

    //编辑用户
    public function edit()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            //验证数据
            try {
                $validate = validate(UsersValidate::class);
                // 获取要排除的id
                $id = $data['id'] ?? null;
                // 动态设置场景规则
                $sceneRules = $validate->sceneBackEdit($id);
                $validate->scene('back-edit', $sceneRules);
                $validate->check($data);
            } catch (ValidateException $e) {
                // 验证失败 输出错误信息
                return $this->error($e->getError());
            }

            if(trim($data['new_password']) != ""){
                // 生成salt
                $data['salt'] = generateSalt();
                // 明码进行加盐hash解决
                $data['password'] = generateHashPassword(trim($data['new_password']), $data['salt']);
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("User")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("User")->where("id", $id)->find();

            $country = getCountry();

            $roles = Db::name("Roles")->select();

            return view("", [
                "getone" => $getone,
                'country' => $country,
                "roles" => $roles,
            ]);
        }
    }


    //删除用户
    public function del()
    {
        $id = input('id');
        $s = Db::name("User")->where("id", $id)->delete();
        if ($s) {
            $this->success("删除成功！");
        } else {
            $this->error("删除失败！");
        }
    }

    //角色列表
    public function roles()
    {
        $List = Db::name('Roles')
            ->order("id asc")
            ->paginate([
                'query'     =>  request()->param(), //url额外参数
                'list_rows' => 20, //每页数量
            ]);

        return view("", [
            "List" => $List
        ]);
    }

    //修改角色
    public function edit_roles()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("Roles")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = input('id');
            $getone = Db::name("Roles")->where("id", $id)->find();

            return view("", [
                "getone" => $getone,
            ]);
        }
    }

    //角色菜单权限
    public function roles_menus(){
        if ($this->request->isPost()) {
            $data = input('post.');

            $menus = json_decode($data['menus']);
            if(empty($data['id']) || empty($menus)){
                $this->error("参数错误！");
            }

            Db::name("Role_backend_menus")->where("role_id", $data['id'])->delete();
            foreach($menus as $menu_id=>$state){
                Db::name("Role_backend_menus")->insert([
                    "role_id" => $data['id'],
                    "menu_id" => $menu_id,
                    "state" => $state,
                ]);
            }

            $this->success('修改成功！');
        } else {
            $id = input('id');
            $getone = Db::name("Roles")->where("id", $id)->find();

            //后台菜单列表
            $menus = Db::name("Backend_menus")
            ->field("id, title, icon, url, parent_id")
            ->where(["parent_id"=>0, "status"=>1])
            ->order("sort asc")
            ->select()
            ->each(function ($item) {
                $item['son'] = Db::name("Backend_menus")
                    ->field("id, title, icon, url, parent_id")
                    ->where(["parent_id"=>$item['id'], "status"=>1])
                    ->order("sort asc")
                    ->select()
                    ->each(function ($i) {
                        $i['son'] = Db::name("Backend_menus")
                            ->field("id, title, icon, url, parent_id")
                            ->where(["parent_id"=>$i['id'], "status"=>1])
                            ->order("sort asc")
                            ->select();

                        return $i;
                    });

                return $item;
            });

            //角色菜单
            $role_menus = Db::name("Role_backend_menus")
                ->where("role_id", $id)
                ->column('state', 'menu_id');

            return view("", [
                "getone" => $getone,
                "menus" => $menus,
                "role_menus" => json_encode($role_menus),
            ]);
        }
    }

    //修改当前登录管理员密码
    public function myeditpwd()
    {
        if ($this->request->isPost()) {
            $data = input('post.');

            //验证数据
            try {
                $validate = validate(UsersValidate::class);
                // 获取要排除的id
                $id = $data['id'] ?? null;
                // 动态设置场景规则
                $sceneRules = $validate->sceneBackEdit($id);
                $validate->scene('back-edit', $sceneRules);
                $validate->check($data);
            } catch (ValidateException $e) {
                // 验证失败 输出错误信息
                return $this->error($e->getError());
            }

            if(trim($data['new_password']) != ""){
                // 生成salt
                $data['salt'] = generateSalt();
                // 明码进行加盐hash解决
                $data['password'] = generateHashPassword(trim($data['new_password']), $data['salt']);
            }

            $data['update_time'] = date("Y-m-d H:i:s");
            $s = Db::name("User")->strict(false)->save($data);
            if ($s) {
                $this->success('修改成功！');
            } else {
                $this->error("修改失败，请重试！");
            }
        } else {
            $id = session('adminId');
            $getone = Db::name("User")->where("id", $id)->find();

            return view("", [
                "getone" => $getone
            ]);
        }
    }
}
